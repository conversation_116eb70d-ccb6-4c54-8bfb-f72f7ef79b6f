# Deployment Guide

This guide will help you deploy your portfolio to various hosting platforms.

## Quick Start Checklist

Before deploying, make sure you have:

- [ ] Updated personal information in `js/config.js`
- [ ] Added your profile photo to `assets/img/profile.jpg`
- [ ] Added your resume to `assets/docs/KuppusamyE_Resume.pdf`
- [ ] Updated contact form email in `js/config.js`
- [ ] Updated social media links
- [ ] Tested the portfolio locally

## Deployment Options

### 1. GitHub Pages (Free)

**Steps:**
1. Create a GitHub repository
2. Upload your portfolio files
3. Go to Settings > Pages
4. Select source: Deploy from a branch
5. Choose branch: `main` or `master`
6. Your site will be available at: `https://username.github.io/repository-name`

**Pros:** Free, automatic deployments, custom domain support
**Cons:** Public repositories only (for free accounts)

### 2. Netlify (Free tier available)

**Steps:**
1. Sign up at [netlify.com](https://netlify.com)
2. Connect your GitHub repository
3. Deploy settings:
   - Build command: (leave empty)
   - Publish directory: (leave empty or `/`)
4. Click "Deploy site"

**Pros:** Automatic deployments, form handling, custom domains, HTTPS
**Cons:** Limited build minutes on free tier

### 3. Vercel (Free tier available)

**Steps:**
1. Sign up at [vercel.com](https://vercel.com)
2. Import your GitHub repository
3. Deploy with default settings
4. Your site will be live instantly

**Pros:** Excellent performance, automatic deployments, custom domains
**Cons:** Limited bandwidth on free tier

### 4. Firebase Hosting (Free tier available)

**Steps:**
1. Install Firebase CLI: `npm install -g firebase-tools`
2. Login: `firebase login`
3. Initialize: `firebase init hosting`
4. Deploy: `firebase deploy`

**Pros:** Google infrastructure, excellent performance
**Cons:** Requires Firebase CLI setup

## Custom Domain Setup

### For GitHub Pages:
1. Add a `CNAME` file with your domain
2. Update DNS settings with your domain provider
3. Enable HTTPS in repository settings

### For Netlify/Vercel:
1. Add domain in dashboard
2. Update DNS settings as instructed
3. SSL certificates are automatic

## Environment Variables

If you need to hide sensitive information:

1. Create environment variables in your hosting platform
2. Update `js/config.js` to use environment variables
3. Use build-time replacement for static sites

## Performance Optimization

Before deploying:

1. **Optimize images**: Use WebP format when possible
2. **Minify CSS/JS**: Use build tools if needed
3. **Enable compression**: Most platforms do this automatically
4. **Test performance**: Use Google PageSpeed Insights

## SEO Optimization

1. **Update meta tags** in `index.html`
2. **Add structured data** (JSON-LD)
3. **Create sitemap.xml**
4. **Submit to Google Search Console**

## Monitoring

After deployment:

1. **Set up analytics**: Google Analytics, Plausible, etc.
2. **Monitor uptime**: UptimeRobot, Pingdom
3. **Check performance**: Regular PageSpeed tests
4. **Update content**: Keep projects and skills current

## Troubleshooting

### Common Issues:

**404 errors on refresh:**
- Add `_redirects` file for Netlify: `/* /index.html 200`
- Configure routing for other platforms

**Form not working:**
- Check FormSubmit email configuration
- Verify form action URL
- Test with different email providers

**Slow loading:**
- Optimize images
- Check external library loading
- Use CDN for assets

**Mobile issues:**
- Test on real devices
- Check viewport meta tag
- Verify touch interactions

## Security

1. **HTTPS only**: Ensure all platforms use HTTPS
2. **Content Security Policy**: Add CSP headers if possible
3. **Regular updates**: Keep dependencies updated
4. **Backup**: Regular backups of your code

## Maintenance

Monthly tasks:
- [ ] Update project information
- [ ] Check all links work
- [ ] Review and update skills
- [ ] Monitor site performance
- [ ] Update dependencies if needed

Need help? Check the main README.md or create an issue in the repository.
