# KUPPUSAMY E - Professional Portfolio

A modern, responsive portfolio website showcasing skills, projects, and experience in Chemical Engineering and Web Development.

## 🌟 Features

- **Responsive Design**: Works perfectly on all devices (mobile, tablet, desktop)
- **Dark/Light Theme**: Toggle between themes with system preference detection
- **Smooth Animations**: AOS (Animate On Scroll) library for engaging user experience
- **Interactive Elements**: Particle background, tilt effects, and smooth scrolling
- **Project Filtering**: Dynamic filtering of projects by category
- **Contact Form**: Functional contact form with FormSubmit integration
- **Performance Optimized**: Lazy loading, efficient animations, and clean code
- **Accessibility**: ARIA labels, keyboard navigation, and screen reader friendly

## 🛠️ Technologies Used

- **HTML5**: Semantic markup and structure
- **CSS3**: Custom styles with Tailwind CSS framework
- **JavaScript (ES6+)**: Modern JavaScript with modular architecture
- **External Libraries**:
  - Tailwind CSS (Styling framework)
  - AOS (Animate On Scroll)
  - Typed.js (Typing animation)
  - Lucide Icons (Icon library)
  - VanillaTilt.js (3D tilt effects)
  - tsParticles (Particle background)

## 📁 Project Structure

```
Kuppu Portfolio/
├── index.html              # Main HTML file
├── css/
│   └── style.css           # Custom CSS styles
├── js/
│   ├── config.js           # Configuration file
│   └── script.js           # Main JavaScript functionality
├── assets/
│   ├── img/                # Images and graphics
│   ├── docs/               # Documents (resume, certificates)
│   └── README.md           # Assets documentation
├── manifest.json           # PWA manifest
├── service-worker.js       # Service worker for offline functionality
├── offline.html            # Offline page
└── README.md               # This file
```

## 🚀 Getting Started

### Prerequisites
- A modern web browser
- Basic knowledge of HTML, CSS, and JavaScript (for customization)

### Installation

1. **Clone or download** this repository
2. **Open `index.html`** in your web browser
3. **Customize** the content by editing the configuration and HTML files

### Customization

#### 1. Personal Information
Edit `js/config.js` to update:
- Name and title
- Contact information
- Social media links
- Skills and percentages
- Animation settings

#### 2. Content Updates
Edit `index.html` to modify:
- About section content
- Project descriptions and links
- Experience timeline
- Achievements and certifications

#### 3. Styling
Edit `css/style.css` to customize:
- Colors and themes
- Animations and transitions
- Layout adjustments

#### 4. Assets
Add your files to the `assets/` directory:
- Profile photo: `assets/img/profile.jpg`
- Resume: `assets/docs/KuppusamyE_Resume.pdf`
- Project screenshots in `assets/img/`

## 📱 Progressive Web App (PWA)

This portfolio includes PWA features:
- **Offline functionality**: Works without internet connection
- **App-like experience**: Can be installed on mobile devices
- **Fast loading**: Service worker caching for improved performance

## 🎨 Customization Guide

### Changing Colors
Update the Tailwind config in `index.html`:
```javascript
colors: {
    brand: {
        // Update these color values
        500: '#your-color',
        600: '#your-darker-color'
    }
}
```

### Adding New Sections
1. Add HTML structure in `index.html`
2. Add navigation link in the header
3. Update the sections array in `js/script.js`
4. Add corresponding styles in `css/style.css`

### Modifying Animations
Edit the AOS settings in `js/config.js`:
```javascript
animations: {
    duration: 700,        // Animation duration
    easing: 'ease-out-cubic', // Easing function
    offset: 60,           // Trigger offset
    once: true            // Animate only once
}
```

## 📧 Contact Form Setup

The contact form uses FormSubmit.co for handling form submissions:

1. **Update the form action** in `js/config.js`:
   ```javascript
   contactForm: {
       action: "https://formsubmit.co/<EMAIL>"
   }
   ```

2. **Alternative services**:
   - Netlify Forms (for Netlify hosting)
   - Formspree
   - EmailJS

## 🌐 Deployment

### GitHub Pages
1. Push your code to a GitHub repository
2. Go to Settings > Pages
3. Select source branch (usually `main`)
4. Your site will be available at `https://username.github.io/repository-name`

### Netlify
1. Connect your GitHub repository to Netlify
2. Deploy automatically on every push
3. Custom domain support available

### Vercel
1. Import your GitHub repository
2. Automatic deployments and preview URLs
3. Excellent performance optimization

## 🔧 Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge
- Mobile browsers (iOS Safari, Chrome Mobile)

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions, issues, and feature requests are welcome! Feel free to check the issues page.

## 📞 Support

If you have any questions or need help customizing the portfolio:
- Email: <EMAIL>
- Create an issue in this repository

---

**Made with ❤️ by KUPPUSAMY E**