# Components Directory

## Purpose
Contains reusable HTML components and templates for modular development.

## Structure
- `header.html` - Navigation header component
- `footer.html` - Footer component
- `project-card.html` - Project card template
- `skill-card.html` - Skill card template
- `contact-form.html` - Contact form component

## Usage
These components can be used for:
- Server-side includes (if using a server)
- Template reference for consistency
- Easy maintenance and updates
- Component-based development

## Benefits
- Consistent design across pages
- Easy to update common elements
- Modular code organization
- Reusable components for future projects

## Note
Currently using inline HTML in index.html, but these components
provide templates for future modularization.
