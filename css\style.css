/* ===== CUSTOM STYLES ===== */

/* Glass effect */
.glass {
    backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, .6);
}

.dark .glass {
    background: rgba(17, 24, 39, .6);
}

/* Smooth scrolling with offset for fixed header */
html {
    scroll-padding-top: 80px;
}

/* Focus ring for accessibility */
.focus-ring:focus {
    outline: 3px solid #3b82f6;
    outline-offset: 2px;
    border-radius: 0.75rem;
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
    [data-aos] {
        transition: none !important;
        transform: none !important;
    }
}

/* Custom animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Hover effects */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Button styles */
.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
    border: 2px solid #e5e7eb;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
}

/* Project card enhancements */
.project-card {
    transition: all 0.3s ease;
    overflow: hidden;
}

.project-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.project-card img {
    transition: transform 0.3s ease;
}

.project-card:hover img {
    transform: scale(1.05);
}

/* Skill bar animations */
.skill-bar {
    transition: width 1.5s ease-out;
    position: relative;
    overflow: hidden;
}

.skill-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: translateX(-100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

/* Navigation enhancements */
.nav-link {
    position: relative;
    transition: color 0.3s ease;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #3b82f6;
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Form enhancements */
.form-input {
    transition: all 0.3s ease;
}

.form-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, .3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}