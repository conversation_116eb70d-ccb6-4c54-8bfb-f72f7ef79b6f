# Data Directory

## Purpose
Contains JSON data files for dynamic content management.

## Structure
- `projects.json` - Project information and metadata
- `skills.json` - Skills data with proficiency levels
- `experience.json` - Work experience and timeline
- `achievements.json` - Certifications and accomplishments
- `personal.json` - Personal information and contact details

## Benefits
- Separate content from presentation
- Easy to update without touching HTML
- Potential for dynamic loading
- Better organization of data
- Easier maintenance and updates

## Usage
These JSON files can be loaded via JavaScript to:
- Populate project cards dynamically
- Update skill levels
- Manage timeline content
- Centralize personal information

## Future Enhancement
Can be integrated with a CMS or headless CMS for
easier content management.
