{"projects": [{"id": "flare-gas-recovery", "title": "Flare Gas Recovery System", "description": "Designed and simulated recovery to reduce emissions by ~30% and recover hydrocarbons. (DWSIM, Excel)", "category": "simulation", "technologies": ["DWSIM", "Excel", "Process Simulation"], "image": "https://images.unsplash.com/photo-1581091014534-8987c1d64763?q=80&w=1200&auto=format&fit=crop", "liveUrl": "#", "codeUrl": "#", "featured": true, "status": "completed", "year": 2024}, {"id": "solar-cleaning-system", "title": "Self-Cleaning Solar Panel System", "description": "Automated wiper + time-based control with vibration cleaning to regain panel efficiency. (<PERSON><PERSON><PERSON><PERSON>, IoT)", "category": "iot", "technologies": ["<PERSON><PERSON><PERSON><PERSON>", "IoT", "Sensors", "Automation"], "image": "https://images.unsplash.com/photo-1509395176047-4a66953fd231?q=80&w=1200&auto=format&fit=crop", "liveUrl": "#", "codeUrl": "#", "featured": true, "status": "completed", "year": 2024}, {"id": "hazard-zone-visualizer", "title": "Hazard Zone Visualizer (Web)", "description": "Select conditions and visualize indicative hazard zoning for education. (HTML, CSS, JS)", "category": "web", "technologies": ["HTML", "CSS", "JavaScript", "Data Visualization"], "image": "https://images.unsplash.com/photo-1555949963-ff9fe0c870eb?q=80&w=1200&auto=format&fit=crop", "liveUrl": "#", "codeUrl": "#", "featured": true, "status": "completed", "year": 2024}]}