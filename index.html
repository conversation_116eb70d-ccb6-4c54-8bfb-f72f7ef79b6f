<!DOCTYPE html>
<html lang="en" class="scroll-smooth">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>KUPPUSAMY E</title>
    <meta name="description"
        content="Portfolio of KUPPUSAMY E — Final year Chemical Engineer showcasing skills in process safety, DWSIM simulation, web development, and innovative projects." />
    <meta name="keywords"
        content="Chemical Engineer, DWSIM, Process Safety, Web Developer, Portfolio, MATLAB, AutoCAD, Final Year Student" />
    <meta name="author" content="KUPPUSAMY E" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://your-domain.com/" />
    <meta property="og:title" content="KUPPUSAMY E — Chemical Engineer & Web Developer" />
    <meta property="og:description"
        content="Final year Chemical Engineer specializing in process safety, simulation, and web development. View my projects and experience." />
    <meta property="og:image" content="assets/img/og-cover.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://your-domain.com/" />
    <meta property="twitter:title" content="KUPPUSAMY E — Chemical Engineer & Web Developer" />
    <meta property="twitter:description"
        content="Final year Chemical Engineer specializing in process safety, simulation, and web development." />
    <meta property="twitter:image" content="assets/img/og-cover.png" />

    <!-- Theme and Icons -->
    <meta name="theme-color" content="#1d4ed8" />
    <link rel="icon" type="image/x-icon" href="assets/icons/favicon.ico" />
    <link rel="apple-touch-icon" href="assets/icons/apple-touch-icon.png" />
    <link rel="manifest" href="manifest.json" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800&family=Rubik:wght@300;400;600&display=swap"
        rel="stylesheet">
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: { sans: ['Rubik', 'Inter', 'ui-sans-serif', 'system-ui'] },
                    colors: { brand: { 50: '#eff6ff', 100: '#dbeafe', 200: '#bfdbfe', 300: '#93c5fd', 400: '#60a5fa', 500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8', 800: '#1e40af', 900: '#1e3a8a' } },
                    boxShadow: { glass: '0 8px 30px rgba(0,0,0,.08)' }
                }
            },
            darkMode: 'class'
        }
    </script>
    <!-- AOS (scroll animations) -->
    <link rel="stylesheet" href="https://unpkg.com/aos@2.3.4/dist/aos.css" />
    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>
    <!-- Typed.js -->
    <script src="https://unpkg.com/typed.js@2.1.0/dist/typed.umd.js"></script>
    <!-- VanillaTilt -->
    <script src="https://unpkg.com/vanilla-tilt@1.8.1/dist/vanilla-tilt.min.js"></script>
    <!-- tsparticles (particles background) -->
    <script src="https://cdn.jsdelivr.net/npm/tsparticles@3/tsparticles.bundle.min.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>

<body class="bg-white text-gray-900 dark:bg-gray-950 dark:text-gray-100">
    <!-- Header / Nav -->
    <header class="fixed top-0 inset-x-0 z-40">
        <nav class="mx-auto max-w-6xl px-4 py-3 flex items-center justify-between glass rounded-b-2xl shadow-glass">
            <a href="#home" class="font-extrabold tracking-tight text-xl">KUPPUSAMY <span
                    class="text-brand-500">E</span></a>
            <button id="menuBtn" class="md:hidden p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-800"
                aria-label="Open menu">
                <i data-lucide="menu"></i>
            </button>
            <ul id="navLinks" class="hidden md:flex items-center gap-6 text-sm font-medium">
                <li><a class="nav-link hover:text-brand-500 transition-colors duration-300" href="#about">About</a></li>
                <li><a class="nav-link hover:text-brand-500 transition-colors duration-300" href="#skills">Skills</a>
                </li>
                <li><a class="nav-link hover:text-brand-500 transition-colors duration-300"
                        href="#projects">Projects</a></li>
                <li><a class="nav-link hover:text-brand-500 transition-colors duration-300"
                        href="#experience">Experience</a></li>
                <li><a class="nav-link hover:text-brand-500 transition-colors duration-300"
                        href="#achievements">Achievements</a></li>
                <li><a class="nav-link hover:text-brand-500 transition-colors duration-300" href="#contact">Contact</a>
                </li>
                <li>
                    <button id="themeToggle"
                        class="ml-2 inline-flex items-center gap-2 px-3 py-1.5 rounded-xl border border-gray-200 dark:border-gray-800 hover:bg-gray-100 dark:hover:bg-gray-800 focus-ring transition-all duration-300"
                        aria-label="Toggle theme">
                        <i data-lucide="sun"></i>
                        <span class="hidden sm:inline">Theme</span>
                    </button>
                </li>
            </ul>
        </nav>
        <!-- Mobile drawer -->
        <div id="drawer"
            class="md:hidden hidden absolute inset-x-0 top-full bg-white/95 dark:bg-gray-900/95 shadow-xl border-t border-gray-100 dark:border-gray-800">
            <ul class="max-w-6xl mx-auto px-4 py-4 grid gap-3 text-sm">
                <li><a class="block px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
                        href="#about">About</a></li>
                <li><a class="block px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
                        href="#skills">Skills</a></li>
                <li><a class="block px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
                        href="#projects">Projects</a></li>
                <li><a class="block px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
                        href="#experience">Experience</a></li>
                <li><a class="block px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
                        href="#achievements">Achievements</a></li>
                <li><a class="block px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
                        href="#contact">Contact</a></li>
                <li>
                    <button id="themeToggleM"
                        class="w-full inline-flex items-center justify-center gap-2 px-3 py-2 rounded-xl border border-gray-200 dark:border-gray-800 hover:bg-gray-100 dark:hover:bg-gray-800">Toggle
                        Theme</button>
                </li>
            </ul>
        </div>
    </header>

    <!-- Hero -->
    <section id="home" class="relative min-h-[90vh] flex items-center">
        <div id="tsparticles" class="absolute inset-0 -z-10"></div>
        <div class="mx-auto max-w-6xl px-4 grid md:grid-cols-2 gap-10 items-center">
            <div data-aos="fade-up">
                <p class="text-sm uppercase tracking-widest text-brand-600/80">Portfolio</p>
                <h1 class="mt-2 text-4xl sm:text-5xl font-extrabold leading-tight">Hi, I'm <span
                        class="text-brand-600">KUPPUSAMY E</span></h1>
                <p class="mt-2 text-base text-gray-600 dark:text-gray-400">Final Year Chemical Engineer • Web Developer
                    • AI Enthusiast</p>
                <p class="mt-4 text-lg text-gray-700 dark:text-gray-300"><span id="typed" class="font-semibold"></span>
                </p>
                <div class="mt-8 flex flex-wrap gap-3">
                    <a href="#projects"
                        class="btn-primary px-5 py-2.5 rounded-2xl bg-brand-600 text-white hover:bg-brand-700 shadow-glass transition-all duration-300 hover-lift">
                        <i data-lucide="folder" class="inline w-4 h-4 mr-2"></i>View Projects
                    </a>
                    <a href="#contact"
                        class="btn-secondary px-5 py-2.5 rounded-2xl border border-gray-200 dark:border-gray-800 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-300">
                        <i data-lucide="mail" class="inline w-4 h-4 mr-2"></i>Contact Me
                    </a>
                    <a href="assets/docs/KuppusamyE_Resume.pdf" target="_blank"
                        class="px-5 py-2.5 rounded-2xl bg-gray-900 text-white dark:bg-white dark:text-gray-900 hover:opacity-90 transition-all duration-300 hover-lift">
                        <i data-lucide="download" class="inline w-4 h-4 mr-2"></i>Download CV
                    </a>
                </div>
            </div>
            <div class="relative" data-aos="fade-left">
                <div class="glass rounded-2xl p-6 shadow-glass">
                    <img loading="lazy"
                        src="https://images.unsplash.com/photo-1522075469751-3a6694fb2f61?q=80&w=1080&auto=format&fit=crop"
                        alt="Profile placeholder" class="w-full aspect-square object-cover rounded-2xl" />
                    <div class="mt-4 grid grid-cols-3 gap-2 text-center text-xs">
                        <span class="px-3 py-2 rounded-xl bg-white/60 dark:bg-gray-800/60">Process Safety</span>
                        <span class="px-3 py-2 rounded-xl bg-white/60 dark:bg-gray-800/60">DWSIM</span>
                        <span class="px-3 py-2 rounded-xl bg-white/60 dark:bg-gray-800/60">Web Tools</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About -->
    <section id="about" class="py-20">
        <div class="mx-auto max-w-6xl px-4">
            <h2 class="text-3xl font-extrabold">About Me</h2>
            <p class="mt-4 text-gray-700 dark:text-gray-300 max-w-3xl" data-aos="fade-up">
                I’m a final-year student passionate about process optimization, plant safety, and sustainable
                technologies. I build
                practical simulations and simple web tools for chemical engineering workflows. Based in Tamil Nadu,
                India.
            </p>
        </div>
    </section>

    <!-- Skills -->
    <section id="skills" class="py-20 bg-gray-50 dark:bg-gray-900/40">
        <div class="mx-auto max-w-6xl px-4">
            <h2 class="text-3xl font-extrabold">Skills</h2>
            <div class="mt-8 grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="p-6 rounded-2xl glass shadow-glass" data-tilt data-tilt-max="8" data-aos="fade-up">
                    <h3 class="font-semibold">Core Tools</h3>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">DWSIM, MATLAB, AutoCAD</p>
                </div>
                <div class="p-6 rounded-2xl glass shadow-glass" data-tilt data-aos="fade-up" data-aos-delay="100">
                    <h3 class="font-semibold">Web Basics</h3>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">HTML, CSS, JavaScript</p>
                </div>
                <div class="p-6 rounded-2xl glass shadow-glass" data-tilt data-aos="fade-up" data-aos-delay="200">
                    <h3 class="font-semibold">Engineering</h3>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">Heat & Mass Transfer, HSE Basics</p>
                </div>
            </div>

            <!-- Animated progress bars -->
            <div class="mt-10 grid md:grid-cols-2 gap-6">
                <div>
                    <div class="flex justify-between text-sm"><span>DWSIM</span><span>85%</span></div>
                    <div class="h-3 rounded-full bg-gray-200 dark:bg-gray-800 overflow-hidden">
                        <div class="skill-bar h-full rounded-full bg-brand-600 w-0" data-target="85"></div>
                    </div>
                </div>
                <div>
                    <div class="flex justify-between text-sm"><span>MATLAB</span><span>75%</span></div>
                    <div class="h-3 rounded-full bg-gray-200 dark:bg-gray-800 overflow-hidden">
                        <div class="skill-bar h-full rounded-full bg-brand-600 w-0" data-target="75"></div>
                    </div>
                </div>
                <div>
                    <div class="flex justify-between text-sm"><span>HTML/CSS</span><span>80%</span></div>
                    <div class="h-3 rounded-full bg-gray-200 dark:bg-gray-800 overflow-hidden">
                        <div class="skill-bar h-full rounded-full bg-brand-600 w-0" data-target="80"></div>
                    </div>
                </div>
                <div>
                    <div class="flex justify-between text-sm"><span>JavaScript</span><span>65%</span></div>
                    <div class="h-3 rounded-full bg-gray-200 dark:bg-gray-800 overflow-hidden">
                        <div class="skill-bar h-full rounded-full bg-brand-600 w-0" data-target="65"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects -->
    <section id="projects" class="py-20">
        <div class="mx-auto max-w-6xl px-4">
            <div class="flex items-end justify-between gap-4">
                <h2 class="text-3xl font-extrabold">Projects</h2>
            </div>
            <!-- Filters -->
            <div class="mt-6 flex flex-wrap gap-2" role="tablist" aria-label="Project filters">
                <button class="filter-btn px-3 py-1.5 rounded-xl border border-gray-200 dark:border-gray-800"
                    data-filter="all" aria-selected="true">All</button>
                <button class="filter-btn px-3 py-1.5 rounded-xl border border-gray-200 dark:border-gray-800"
                    data-filter="simulation">Simulation</button>
                <button class="filter-btn px-3 py-1.5 rounded-xl border border-gray-200 dark:border-gray-800"
                    data-filter="web">Web</button>
                <button class="filter-btn px-3 py-1.5 rounded-xl border border-gray-200 dark:border-gray-800"
                    data-filter="iot">IoT</button>
            </div>

            <div class="mt-8 grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Card 1 -->
                <article class="group rounded-2xl overflow-hidden glass shadow-glass project-card"
                    data-category="simulation" data-tilt data-aos="fade-up">
                    <img loading="lazy" class="h-44 w-full object-cover"
                        src="https://images.unsplash.com/photo-1581091014534-8987c1d64763?q=80&w=1200&auto=format&fit=crop"
                        alt="Flare Gas Recovery" />
                    <div class="p-5">
                        <h3 class="font-semibold">Flare Gas Recovery System</h3>
                        <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">Designed and simulated recovery to
                            reduce emissions by ~30% and recover hydrocarbons. (DWSIM, Excel)</p>
                        <div class="mt-4 flex gap-3 text-sm">
                            <a class="px-3 py-1.5 rounded-xl border border-gray-200 dark:border-gray-800 hover:bg-gray-100 dark:hover:bg-gray-800"
                                href="#" aria-label="Live demo">Live</a>
                            <a class="px-3 py-1.5 rounded-xl bg-brand-600 text-white hover:bg-brand-700" href="#"
                                aria-label="Source code">Code</a>
                        </div>
                    </div>
                </article>
                <!-- Card 2 -->
                <article class="group rounded-2xl overflow-hidden glass shadow-glass project-card" data-category="iot"
                    data-tilt data-aos="fade-up" data-aos-delay="100">
                    <img loading="lazy" class="h-44 w-full object-cover"
                        src="https://images.unsplash.com/photo-1509395176047-4a66953fd231?q=80&w=1200&auto=format&fit=crop"
                        alt="Solar Cleaning" />
                    <div class="p-5">
                        <h3 class="font-semibold">Self-Cleaning Solar Panel System</h3>
                        <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">Automated wiper + time-based control
                            with vibration cleaning to regain panel efficiency. (Arduino, IoT)</p>
                        <div class="mt-4 flex gap-3 text-sm">
                            <a class="px-3 py-1.5 rounded-xl border border-gray-200 dark:border-gray-800 hover:bg-gray-100 dark:hover:bg-gray-800"
                                href="#">Live</a>
                            <a class="px-3 py-1.5 rounded-xl bg-brand-600 text-white hover:bg-brand-700"
                                href="#">Code</a>
                        </div>
                    </div>
                </article>
                <!-- Card 3 -->
                <article class="group rounded-2xl overflow-hidden glass shadow-glass project-card" data-category="web"
                    data-tilt data-aos="fade-up" data-aos-delay="200">
                    <img loading="lazy" class="h-44 w-full object-cover"
                        src="https://images.unsplash.com/photo-1555949963-ff9fe0c870eb?q=80&w=1200&auto=format&fit=crop"
                        alt="Hazard Visualizer" />
                    <div class="p-5">
                        <h3 class="font-semibold">Hazard Zone Visualizer (Web)</h3>
                        <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">Select conditions and visualize
                            indicative hazard zoning for education. (HTML, CSS, JS)</p>
                        <div class="mt-4 flex gap-3 text-sm">
                            <a class="px-3 py-1.5 rounded-xl border border-gray-200 dark:border-gray-800 hover:bg-gray-100 dark:hover:bg-gray-800"
                                href="#">Live</a>
                            <a class="px-3 py-1.5 rounded-xl bg-brand-600 text-white hover:bg-brand-700"
                                href="#">Code</a>
                        </div>
                    </div>
                </article>
            </div>
        </div>
    </section>

    <!-- Experience -->
    <section id="experience" class="py-20 bg-gray-50 dark:bg-gray-900/40">
        <div class="mx-auto max-w-6xl px-4">
            <h2 class="text-3xl font-extrabold">Experience</h2>
            <ol class="mt-8 relative border-s border-gray-200 dark:border-gray-800">
                <li class="mb-10 ms-6" data-aos="fade-up">
                    <span
                        class="absolute -start-3.5 flex h-7 w-7 items-center justify-center rounded-full bg-brand-600 text-white">1</span>
                    <h3 class="font-semibold">Intern — Safety Design, CPCL</h3>
                    <time class="block text-xs text-gray-500">May 2025 – Jun 2025</time>
                    <ul class="mt-2 text-sm list-disc ms-5 text-gray-700 dark:text-gray-300">
                        <li>Observed safety interlocks across distillation units.</li>
                        <li>Compiled energy-saving suggestions with calculations.</li>
                    </ul>
                </li>
            </ol>
        </div>
    </section>

    <!-- Achievements -->
    <section id="achievements" class="py-20">
        <div class="mx-auto max-w-6xl px-4">
            <h2 class="text-3xl font-extrabold">Achievements & Certifications</h2>
            <div class="mt-8 grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="p-6 rounded-2xl glass shadow-glass" data-aos="zoom-in">
                    <p class="font-semibold">NPTEL — Process Simulation using DWSIM</p>
                </div>
                <div class="p-6 rounded-2xl glass shadow-glass" data-aos="zoom-in" data-aos-delay="100">
                    <p class="font-semibold">Winner — Paper Presentation, PetroExpo’25</p>
                </div>
                <div class="p-6 rounded-2xl glass shadow-glass" data-aos="zoom-in" data-aos-delay="200">
                    <p class="font-semibold">AICTE Workshop — Green Energy & HSE</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Resume -->
    <section id="resume" class="py-20 bg-gray-50 dark:bg-gray-900/40">
        <div class="mx-auto max-w-6xl px-4 text-center">
            <h2 class="text-3xl font-extrabold">Resume</h2>
            <p class="mt-3 text-gray-700 dark:text-gray-300">Download my latest resume for full details.</p>
            <a href="assets/KuppusamyE_Resume.pdf"
                class="inline-flex mt-6 px-6 py-3 rounded-2xl bg-brand-600 text-white hover:bg-brand-700 shadow-glass">Download
                Resume</a>
        </div>
    </section>

    <!-- Contact -->
    <section id="contact" class="py-20">
        <div class="mx-auto max-w-6xl px-4">
            <h2 class="text-3xl font-extrabold">Contact</h2>
            <div class="mt-8 grid md:grid-cols-2 gap-8">
                <form class="p-6 rounded-2xl glass shadow-glass grid gap-4"
                    action="https://formsubmit.co/<EMAIL>" method="POST" data-aos="fade-right">
                    <input type="hidden" name="_captcha" value="false">
                    <input type="hidden" name="_next" value="https://your-domain.com/thank-you.html">
                    <input type="hidden" name="_subject" value="New Portfolio Contact Form Submission">

                    <label class="grid gap-1 text-sm">
                        <span class="font-medium">Name <span class="text-red-500">*</span></span>
                        <input required name="name"
                            class="form-input px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-800 bg-white/70 dark:bg-gray-900/70 focus-ring transition-all duration-300"
                            placeholder="Your full name" />
                    </label>

                    <label class="grid gap-1 text-sm">
                        <span class="font-medium">Email <span class="text-red-500">*</span></span>
                        <input required type="email" name="email"
                            class="form-input px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-800 bg-white/70 dark:bg-gray-900/70 focus-ring transition-all duration-300"
                            placeholder="<EMAIL>" />
                    </label>

                    <label class="grid gap-1 text-sm">
                        <span class="font-medium">Subject</span>
                        <input name="subject"
                            class="form-input px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-800 bg-white/70 dark:bg-gray-900/70 focus-ring transition-all duration-300"
                            placeholder="What's this about?" />
                    </label>

                    <label class="grid gap-1 text-sm">
                        <span class="font-medium">Message <span class="text-red-500">*</span></span>
                        <textarea required name="message" rows="4"
                            class="form-input px-4 py-3 rounded-xl border border-gray-200 dark:border-gray-800 bg-white/70 dark:bg-gray-900/70 focus-ring transition-all duration-300 resize-none"
                            placeholder="Tell me about your project or just say hello!"></textarea>
                    </label>

                    <button type="submit"
                        class="btn-primary mt-2 px-5 py-3 rounded-2xl bg-brand-600 text-white hover:bg-brand-700 transition-all duration-300 hover-lift">
                        <i data-lucide="send" class="inline w-4 h-4 mr-2"></i>Send Message
                    </button>
                </form>
                <div class="p-6 rounded-2xl glass shadow-glass" data-aos="fade-left">
                    <p class="text-sm">You can also reach me at:</p>
                    <ul class="mt-3 text-sm text-gray-700 dark:text-gray-300 space-y-2">
                        <li><strong>Email:</strong> <a class="text-brand-600 hover:underline"
                                href="mailto:<EMAIL>"><EMAIL></a></li>
                        <li><strong>Location:</strong> Tamil Nadu, India</li>
                        <li><strong>LinkedIn:</strong> <a class="text-brand-600 hover:underline" href="#">#</a></li>
                        <li><strong>GitHub:</strong> <a class="text-brand-600 hover:underline" href="#">#</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-10 border-t border-gray-100 dark:border-gray-800">
        <div class="mx-auto max-w-6xl px-4 flex flex-col sm:flex-row items-center justify-between gap-4 text-sm">
            <p>© <span id="year"></span> KUPPUSAMY E</p>
            <div class="flex items-center gap-4">
                <a class="hover:text-brand-600" href="#home">Back to top ↑</a>
            </div>
        </div>
    </footer>

    <!-- External Scripts -->
    <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>
    <!-- Configuration -->
    <script src="js/config.js"></script>
    <!-- Custom JavaScript -->
    <script src="js/script.js"></script>
</body>

</html>