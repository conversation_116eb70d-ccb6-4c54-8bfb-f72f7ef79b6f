// ===== PORTFOLIO CONFIGURATION =====
// Edit these values to customize your portfolio

const portfolioConfig = {
    // Personal Information
    name: "KUPPUSAMY E",
    title: "Final Year Chemical Engineer • Web Developer • AI Enthusiast",
    email: "<EMAIL>",
    location: "Tamil Nadu, India",
    
    // Social Links
    social: {
        linkedin: "#", // Add your LinkedIn URL
        github: "#",   // Add your GitHub URL
        twitter: "#",  // Add your Twitter URL (optional)
        instagram: "#" // Add your Instagram URL (optional)
    },
    
    // Typed Text Animation
    typedStrings: [
        'Final Year Student',
        'Process Safety Enthusiast', 
        'DWSIM • MATLAB • HTML/JS'
    ],
    
    // Skills Configuration
    skills: [
        { name: "<PERSON><PERSON><PERSON>", percentage: 85 },
        { name: "MATL<PERSON>", percentage: 75 },
        { name: "HTML/CSS", percentage: 80 },
        { name: "JavaScript", percentage: 65 }
    ],
    
    // Theme Colors (Tailwind CSS classes)
    colors: {
        primary: "brand-600",
        primaryHover: "brand-700",
        secondary: "gray-600",
        accent: "blue-500"
    },
    
    // Animation Settings
    animations: {
        duration: 700,
        easing: 'ease-out-cubic',
        offset: 60,
        once: true
    },
    
    // Particles Configuration
    particles: {
        number: 45,
        colors: ['#60a5fa', '#93c5fd', '#bfdbfe'],
        speed: 1.2,
        linkDistance: 130,
        linkOpacity: 0.3
    },
    
    // Contact Form
    contactForm: {
        action: "https://formsubmit.co/<EMAIL>", // Update with your email
        successMessage: "Thank you! Your message has been sent.",
        errorMessage: "Sorry, there was an error sending your message."
    }
};

// Make config available globally
window.portfolioConfig = portfolioConfig;
