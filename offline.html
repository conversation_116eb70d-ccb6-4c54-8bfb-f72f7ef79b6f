<!DOCTYPE html>
<html lang="en" class="scroll-smooth">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Offline - KUPPUSAMY E Portfolio</title>
    <meta name="description" content="You're currently offline. Please check your internet connection." />

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: { sans: ['Rubik', 'Inter', 'ui-sans-serif', 'system-ui'] },
                    colors: { brand: { 50: '#eff6ff', 100: '#dbeafe', 200: '#bfdbfe', 300: '#93c5fd', 400: '#60a5fa', 500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8', 800: '#1e40af', 900: '#1e3a8a' } },
                    boxShadow: { glass: '0 8px 30px rgba(0,0,0,.08)' }
                }
            },
            darkMode: 'class'
        }
    </script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">

    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>
</head>

<body class="bg-white text-gray-900 dark:bg-gray-950 dark:text-gray-100">
    <div class="min-h-screen flex items-center justify-center px-4">
        <div class="max-w-md w-full text-center">
            <div class="glass rounded-2xl p-8 shadow-glass">
                <!-- Offline Icon -->
                <div
                    class="mx-auto w-16 h-16 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mb-6">
                    <i data-lucide="wifi-off" class="w-8 h-8 text-orange-600 dark:text-orange-400"></i>
                </div>

                <!-- Offline Message -->
                <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                    You're Offline
                </h1>

                <p class="text-gray-600 dark:text-gray-300 mb-6">
                    It looks like you're not connected to the internet. Please check your connection and try again.
                </p>

                <!-- Cached Content Info -->
                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4 mb-6">
                    <p class="text-sm text-blue-800 dark:text-blue-200">
                        <i data-lucide="info" class="inline w-4 h-4 mr-1"></i>
                        Some content may be available from cache
                    </p>
                </div>

                <!-- Action Buttons -->
                <div class="space-y-3">
                    <button onclick="window.location.reload()"
                        class="block w-full px-6 py-3 bg-brand-600 text-white rounded-xl hover:bg-brand-700 transition-colors duration-300">
                        <i data-lucide="refresh-cw" class="inline w-4 h-4 mr-2"></i>
                        Try Again
                    </button>

                    <button onclick="history.back()"
                        class="block w-full px-6 py-3 border border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-300">
                        <i data-lucide="arrow-left" class="inline w-4 h-4 mr-2"></i>
                        Go Back
                    </button>
                </div>

                <!-- Contact Info -->
                <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">
                        Need to reach me urgently?
                    </p>
                    <a href="mailto:<EMAIL>"
                        class="text-brand-600 hover:text-brand-700 text-sm font-medium">
                        <EMAIL>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }

        // Check connection status
        function updateOnlineStatus() {
            if (navigator.onLine) {
                // If back online, redirect to main page
                window.location.href = 'index.html';
            }
        }

        // Listen for online/offline events
        window.addEventListener('online', updateOnlineStatus);
        window.addEventListener('offline', updateOnlineStatus);

        // Periodic connection check
        setInterval(() => {
            if (navigator.onLine) {
                updateOnlineStatus();
            }
        }, 5000);
    </script>
</body>

</html>