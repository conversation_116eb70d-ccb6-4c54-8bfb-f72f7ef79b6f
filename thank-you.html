<!DOCTYPE html>
<html lang="en" class="scroll-smooth">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Thank You - KUPPUSAMY E</title>
    <meta name="description" content="Thank you for contacting KUPPUSAMY E. Your message has been received." />
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: { sans: ['Rubik', 'Inter', 'ui-sans-serif', 'system-ui'] },
                    colors: { brand: { 50: '#eff6ff', 100: '#dbeafe', 200: '#bfdbfe', 300: '#93c5fd', 400: '#60a5fa', 500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8', 800: '#1e40af', 900: '#1e3a8a' } },
                    boxShadow: { glass: '0 8px 30px rgba(0,0,0,.08)' }
                }
            },
            darkMode: 'class'
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    
    <!-- Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>
</head>

<body class="bg-white text-gray-900 dark:bg-gray-950 dark:text-gray-100">
    <div class="min-h-screen flex items-center justify-center px-4">
        <div class="max-w-md w-full text-center">
            <div class="glass rounded-2xl p-8 shadow-glass">
                <!-- Success Icon -->
                <div class="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mb-6">
                    <i data-lucide="check-circle" class="w-8 h-8 text-green-600 dark:text-green-400"></i>
                </div>
                
                <!-- Thank You Message -->
                <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                    Thank You!
                </h1>
                
                <p class="text-gray-600 dark:text-gray-300 mb-6">
                    Your message has been successfully sent. I'll get back to you as soon as possible.
                </p>
                
                <!-- Action Buttons -->
                <div class="space-y-3">
                    <a href="index.html" 
                       class="block w-full px-6 py-3 bg-brand-600 text-white rounded-xl hover:bg-brand-700 transition-colors duration-300">
                        <i data-lucide="home" class="inline w-4 h-4 mr-2"></i>
                        Back to Portfolio
                    </a>
                    
                    <a href="mailto:<EMAIL>" 
                       class="block w-full px-6 py-3 border border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-300">
                        <i data-lucide="mail" class="inline w-4 h-4 mr-2"></i>
                        Send Direct Email
                    </a>
                </div>
                
                <!-- Additional Info -->
                <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        Expected response time: 24-48 hours
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize icons
        lucide.createIcons();
        
        // Auto redirect after 10 seconds
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 10000);
        
        // Show countdown
        let countdown = 10;
        const countdownElement = document.createElement('p');
        countdownElement.className = 'text-xs text-gray-400 mt-4';
        countdownElement.textContent = `Redirecting to portfolio in ${countdown} seconds...`;
        document.querySelector('.glass').appendChild(countdownElement);
        
        const countdownInterval = setInterval(() => {
            countdown--;
            countdownElement.textContent = `Redirecting to portfolio in ${countdown} seconds...`;
            
            if (countdown <= 0) {
                clearInterval(countdownInterval);
            }
        }, 1000);
    </script>
</body>

</html>
