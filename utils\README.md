# Utils Directory

## Purpose
Contains utility scripts and helper functions for development and maintenance.

## Structure
- `build.js` - Build script for optimization
- `deploy.js` - Deployment automation script
- `image-optimizer.js` - Image compression utility
- `sitemap-generator.js` - Generate sitemap.xml
- `performance-check.js` - Performance testing script

## Development Tools
- `dev-server.js` - Local development server
- `watch.js` - File watching for auto-reload
- `lint.js` - Code linting and formatting

## Maintenance Scripts
- `backup.js` - Backup portfolio data
- `update-dependencies.js` - Update external libraries
- `seo-check.js` - SEO analysis tool

## Usage
Run these scripts via Node.js for:
- Automated deployments
- Performance optimization
- Code quality checks
- Maintenance tasks

## Requirements
- Node.js installed
- npm packages as needed
